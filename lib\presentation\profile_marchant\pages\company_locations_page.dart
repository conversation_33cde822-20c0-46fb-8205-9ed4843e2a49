import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/utils/validators.dart';
import 'package:wesell/data/models/profile_marchant_model.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/presentation/widgets/custom_dropdown_form_field.dart';
import 'package:wesell/presentation/widgets/custom_text_field.dart';
import '../bloc/profile_marchant_bloc.dart';
import '../bloc/profile_marchant_event.dart';
import '../bloc/profile_marchant_state.dart';

class CompanyLocationsPage extends StatefulWidget {
  final ProfileMarchantModel profileData;

  const CompanyLocationsPage({super.key, required this.profileData});

  @override
  _CompanyLocationsPageState createState() => _CompanyLocationsPageState();
}

class _CompanyLocationsPageState extends State<CompanyLocationsPage> {
  final _formKey = GlobalKey<FormState>();
  final _addressController = TextEditingController();
  final _poBoxController = TextEditingController();
  
  String? _selectedCountry;
  String? _selectedCity;
  
  final Map<String, List<String>> _countryToCities = {
    'Saudi Arabia': ['Riyadh', 'Jeddah', 'Mecca', 'Medina', 'Dammam'],
    'Pakistan': ['Islamabad', 'Karachi', 'Lahore', 'Faisalabad', 'Rawalpindi'],
  };

  List<String> get _availableCountries => _countryToCities.keys.toList();
  List<String> get _availableCities => _selectedCountry != null 
      ? _countryToCities[_selectedCountry!] ?? [] 
      : [];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    final locations = widget.profileData.attributes?.companyLocations;
    if (locations != null) {
      _selectedCountry = locations.country;
      _selectedCity = locations.city;
      _addressController.text = locations.address ?? '';
      _poBoxController.text = locations.poBox ?? '';
    }
  }

  void _onCountryChanged(String? country) {
    setState(() {
      _selectedCountry = country;
      _selectedCity = null; // Reset city when country changes
    });
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _handleFormSubmit() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<ProfileMarchantBloc>().add(UpdateCompanyLocationsEvent(
        country: _selectedCountry ?? '',
        city: _selectedCity ?? '',
        address: _addressController.text.trim(),
        poBox: _poBoxController.text.trim(),
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: BlocListener<ProfileMarchantBloc, ProfileMarchantState>(
        listener: (context, state) {
          if (state is UpdateCompanyLocationsSuccess) {
            _showSnackBar(state.message);
            // Refresh profile data after successful update
            context.read<ProfileMarchantBloc>().add(GetProfileEvent());
          } else if (state is UpdateCompanyLocationsError) {
            _showSnackBar(state.message);
          }
        },
        child: Scaffold(
          backgroundColor: AppColors.whiteTheme,
          appBar: AppBar(
            backgroundColor: AppColors.whiteTheme,
            title: const Text(AppStrings.companyLocation),
            leading: const BackButton(color: AppColors.blackTextTheme),
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              autovalidateMode: AutovalidateMode.onUserInteraction,
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Country Dropdown
                  CustomDropdownFormField<String>(
                    value: _selectedCountry,
                    items: _availableCountries,
                    hintText: AppStrings.country,
                    itemLabel: (item) => item,
                    onChanged: _onCountryChanged,
                    validator: (value) => value == null ? 'Please select a country' : null,
                  ),
                  const SizedBox(height: 16),
                  
                  // City Dropdown
                  CustomDropdownFormField<String>(
                    value: _selectedCity,
                    items: _availableCities,
                    hintText: AppStrings.city,
                    itemLabel: (item) => item,
                    onChanged: (value) => setState(() => _selectedCity = value),
                    validator: (value) => value == null ? 'Please select a city' : null,
                  ),
                  const SizedBox(height: 16),
                  
                  // Address Field
                  CustomTextField(
                    controller: _addressController,
                    labelText: AppStrings.address,
                    hintText: AppStrings.address,
                    validator: (value) => Validators.validateRequired(value, AppStrings.address),
                  ),
                  const SizedBox(height: 16),
                  
                  // PO Box Field
                  CustomTextField(
                    controller: _poBoxController,
                    labelText: AppStrings.poBox,
                    hintText: AppStrings.poBox,
                    validator: (value) => Validators.validateRequired(value, AppStrings.poBox),
                  ),
                  const SizedBox(height: 24),
                  
                  // Save Button
                  BlocBuilder<ProfileMarchantBloc, ProfileMarchantState>(
                    builder: (context, state) {
                      return CustomButton(
                        width: double.infinity,
                        backgroundColor: AppColors.primaryTheme,
                        text: AppStrings.saveChanges,
                        onPressed: state is UpdateCompanyLocationsLoading ? null : _handleFormSubmit,
                        isLoading: state is UpdateCompanyLocationsLoading,
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _addressController.dispose();
    _poBoxController.dispose();
    super.dispose();
  }
}
