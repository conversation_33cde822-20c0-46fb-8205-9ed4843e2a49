import 'package:equatable/equatable.dart';
import 'package:wesell/data/models/user_model.dart';

abstract class LoginState extends Equatable {
  const LoginState();

  @override
  List<Object?> get props => [];
}

class LoginInitial extends LoginState {
  const LoginInitial();
}

class LoginLoading extends LoginState {
  const LoginLoading();
}

class LoginSuccess extends LoginState {
  final User user;

  const LoginSuccess({required this.user});

  @override
  List<Object> get props => [user];
}

class LoginFailure extends LoginState {
  final String message;

  const LoginFailure({required this.message});

  @override
  List<Object> get props => [message];
}

class LogoutSuccess extends LoginState {
  const LogoutSuccess();
}

class LogoutFailure extends LoginState {
  final String message;

  const LogoutFailure({required this.message});

  @override
  List<Object> get props => [message];
}

class LoginStatusChecked extends LoginState {
  final bool isLoggedIn;
  final User? user;

  const LoginStatusChecked({
    required this.isLoggedIn,
    this.user,
  });

  @override
  List<Object?> get props => [isLoggedIn, user];
}

class ForgotPasswordLoading extends LoginState {
  const ForgotPasswordLoading();
}

class ForgotPasswordSuccess extends LoginState {
  final String message;

  const ForgotPasswordSuccess({required this.message});

  @override
  List<Object> get props => [message];
}

class ForgotPasswordFailure extends LoginState {
  final String message;

  const ForgotPasswordFailure({required this.message});

  @override
  List<Object> get props => [message];
}
