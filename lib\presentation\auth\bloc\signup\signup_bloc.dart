import 'package:bloc/bloc.dart';
import 'package:wesell/domain/usecases/register_usecase.dart';
import 'package:wesell/domain/usecases/upload_file_usecase.dart';
import 'package:wesell/presentation/auth/bloc/signup/signup_event.dart';
import 'package:wesell/presentation/auth/bloc/signup/signup_state.dart';


class SignupBloc extends Bloc<SignupEvent, SignupState> {
  final RegisterUseCase registerUseCase;
  final UploadFileUseCase uploadFileUseCase;

  SignupBloc({
    required this.registerUseCase,
    required this.uploadFileUseCase,
  }) : super(SignupInitial()) {
    on<SignupSubmitted>(_onSignupSubmitted);
    on<FileUploadSubmitted>(_onFileUploadSubmitted);
  }

  Future<void> _onSignupSubmitted(SignupSubmitted event, Emitter<SignupState> emit) async {
    emit(SignupLoading());
    try {
      final result = await registerUseCase(RegisterParams(
        firstName: event.firstName,
        lastName: event.lastName,
        email: event.email,
        password: event.password,
        type: event.type,
        companyLegalName: event.companyLegalName,
        crNumber: event.crNumber,
        investedCapital: event.investedCapital,
        investedCapitalUnit: event.investedCapitalUnit,
        companySize: event.companySize,
        licenseDocument: event.licenseDocument,
        crDocument: event.crDocument,
      ));
      result.fold(
        (failure) => emit(SignupFailure(failure.message)),
        (response) => emit(SignupSuccess(response.message)),
      );
    } catch (e) {
      emit(SignupFailure(e.toString()));
    }
  }

  Future<void> _onFileUploadSubmitted(FileUploadSubmitted event, Emitter<SignupState> emit) async {
    emit(FileUploadLoading(uploadId: event.uploadId));
    try {
      final result = await uploadFileUseCase(UploadFileParams(
        file: event.file,
        fileName: event.fileName,
      ));
      result.fold(
        (failure) => emit(FileUploadFailure(
          error: failure.message,
          uploadId: event.uploadId,
        )),
        (response) => emit(FileUploadSuccess(
          fileId: response.fileId,
          fileName: response.fileName,
          uploadId: event.uploadId,
        )),
      );
    } catch (e) {
      emit(FileUploadFailure(
        error: e.toString(),
        uploadId: event.uploadId,
      ));
    }
  }
}
