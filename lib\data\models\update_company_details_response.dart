class UpdateCompanyDetailsResponse {
  final bool success;
  final String message;
  final int status;

  UpdateCompanyDetailsResponse({
    required this.success,
    required this.message,
    required this.status,
  });

  factory UpdateCompanyDetailsResponse.fromJson(Map<String, dynamic> json) {
    return UpdateCompanyDetailsResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      status: json['status'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'status': status,
    };
  }
}
