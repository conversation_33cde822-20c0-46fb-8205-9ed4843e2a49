import 'package:equatable/equatable.dart';

abstract class SignupState extends Equatable {
  const SignupState();
  @override
  List<Object?> get props => [];
}

class SignupInitial extends SignupState {}
class SignupLoading extends SignupState {}
class SignupSuccess extends SignupState {
  final String message;
  const SignupSuccess(this.message);
  @override
  List<Object?> get props => [message];
}
class SignupFailure extends SignupState {
  final String error;
  const SignupFailure(this.error);
  @override
  List<Object?> get props => [error];
}

class FileUploadLoading extends SignupState {
  final String uploadId;
  const FileUploadLoading({required this.uploadId});
  @override
  List<Object> get props => [uploadId];
}

class FileUploadSuccess extends SignupState {
  final String fileId;
  final String fileName;
  final String uploadId;

  const FileUploadSuccess({
    required this.fileId,
    required this.fileName,
    required this.uploadId,
  });

  @override
  List<Object> get props => [fileId, fileName, uploadId];
}

class FileUploadFailure extends SignupState {
  final String error;
  final String uploadId;

  const FileUploadFailure({
    required this.error,
    required this.uploadId,
  });

  @override
  List<Object> get props => [error, uploadId];
}
