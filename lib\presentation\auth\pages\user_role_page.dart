import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/routes/app_routes.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';

class UserRolePage extends StatefulWidget {
  const UserRolePage({super.key});

  @override
  State<UserRolePage> createState() => _UserRolePageState();
}

class _UserRolePageState extends State<UserRolePage> {
  String _selectedRole = 'merchant';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor:Colors.transparent,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 40.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              AppStrings.joinAsSellerOrMerchant,
              style: TextStyle(
                color: AppColors.primaryTheme,
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 30),
            _buildRoleOption(
              value: 'merchant',
              icon: Icons.apartment,
              title: AppStrings.merchantTitle,
              subtitle: AppStrings.merchantSubtitle,
            ),
            const SizedBox(height: 16),
            _buildRoleOption(
              value: 'dealMaker',
              icon: Icons.person_outline,
              title: AppStrings.sellerTitle,
              subtitle: AppStrings.sellerSubtitle,
            ),
            const SizedBox(height: 30),
            SizedBox(
              width: double.infinity,
              child: CustomButton(
                height: 48,
                backgroundColor: AppColors.primaryTheme,
                text: _selectedRole == 'merchant'
                  ? AppStrings.joinAsMerchant
                  : AppStrings.joinAsSeller,
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    AppRoutes.signup,
                    arguments: {'selectedRole': _selectedRole},
                  );
                },
              ),
            ),
            const SizedBox(height: 20),
            Center(
              child: Text.rich(
                TextSpan(
                  text: AppStrings.alreadyHaveAccount,
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColors.textgreyTheme,
                  ),
                  children: [
                    TextSpan(
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          // Navigate to login page
                          Navigator.pop(context);
                        },
                      text: AppStrings.signIn,
                      style: const TextStyle(color:  AppColors.primaryTheme,fontSize: 16,),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleOption({
    required String value,
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    final bool selected = _selectedRole == value;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedRole = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: selected ? Colors.green : Colors.grey.shade300,
            width: 1.5,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 32,
              color: selected ? Colors.green : Colors.grey,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(fontSize: 15, fontWeight: FontWeight.w500),
                  ),
                  Text(
                    subtitle,
                    style: const TextStyle(fontSize: 13),
                  ),
                ],
              ),
            ),
            Radio<String>(
              value: value,
              groupValue: _selectedRole,
              activeColor: Colors.green,
              onChanged: (val) {
                setState(() {
                  _selectedRole = val!;
                });
              },
            ),
          ],
        ),
      ),
    );
  }
}
