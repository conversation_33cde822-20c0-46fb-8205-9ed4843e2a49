class User {
  final int exp;
  final int iat;
  final String jti;
  final String iss;
  final List<String> aud;
  final String sub;
  final String typ;
  final String azp;
  final String sessionState;
  final String acr;
  final List<String> allowedOrigins;
  final RealmAccess realmAccess;
  final Map<String, ResourceAccess> resourceAccess;
  final String scope;
  final String sid;
  final String role;
  final bool emailVerified;
  final String name;
  final String preferredUsername;
  final String givenName;
  final String userId;
  final String familyName;
  final String email;

  User({
    required this.exp,
    required this.iat,
    required this.jti,
    required this.iss,
    required this.aud,
    required this.sub,
    required this.typ,
    required this.azp,
    required this.sessionState,
    required this.acr,
    required this.allowedOrigins,
    required this.realmAccess,
    required this.resourceAccess,
    required this.scope,
    required this.sid,
    required this.role,
    required this.emailVerified,
    required this.name,
    required this.preferredUsername,
    required this.givenName,
    required this.userId,
    required this.familyName,
    required this.email,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      exp: json['exp'] as int,
      iat: json['iat'] as int,
      jti: json['jti'] as String,
      iss: json['iss'] as String,
      aud: List<String>.from(json['aud'] as List),
      sub: json['sub'] as String,
      typ: json['typ'] as String,
      azp: json['azp'] as String,
      sessionState: json['session_state'] as String,
      acr: json['acr'] as String,
      allowedOrigins: List<String>.from(json['allowed-origins'] as List),
      realmAccess: RealmAccess.fromJson(json['realm_access'] as Map<String, dynamic>),
      resourceAccess: (json['resource_access'] as Map<String, dynamic>).map(
        (key, value) => MapEntry(key, ResourceAccess.fromJson(value as Map<String, dynamic>)),
      ),
      scope: json['scope'] as String,
      sid: json['sid'] as String,
      role: json['role'] as String,
      emailVerified: json['email_verified'] as bool,
      name: json['name'] as String,
      preferredUsername: json['preferred_username'] as String,
      givenName: json['given_name'] as String,
      userId: json['userId'] as String,
      familyName: json['family_name'] as String,
      email: json['email'] as String,
    );
  }

  factory User.fromJwt(Map<String, dynamic> json) {
    return User(
      exp: json['exp'] as int,
      iat: json['iat'] as int,
      jti: json['jti'] as String,
      iss: json['iss'] as String,
      aud: List<String>.from(json['aud'] is List ? json['aud'] : [json['aud']]),
      sub: json['sub'] as String,
      typ: json['typ'] as String,
      azp: json['azp'] as String,
      sessionState: json['session_state'] as String,
      acr: json['acr'] as String,
      allowedOrigins: List<String>.from(json['allowed-origins'] ?? []),
      realmAccess: json['realm_access'] != null ? RealmAccess.fromJson(json['realm_access'] as Map<String, dynamic>) : RealmAccess(roles: []),
      resourceAccess: (json['resource_access'] != null
        ? (json['resource_access'] as Map<String, dynamic>).map(
            (key, value) => MapEntry(key as String, ResourceAccess.fromJson(value as Map<String, dynamic>)),
          )
        : <String, ResourceAccess>{}),
      scope: json['scope'] as String,
      sid: json['sid'] as String,
      role: json['role'] ?? '',
      emailVerified: json['email_verified'] ?? false,
      name: json['name'] ?? '',
      preferredUsername: json['preferred_username'] ?? '',
      givenName: json['given_name'] ?? '',
      userId: json['userId'] ?? json['sub'] ?? '',
      familyName: json['family_name'] ?? '',
      email: json['email'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'exp': exp,
      'iat': iat,
      'jti': jti,
      'iss': iss,
      'aud': aud,
      'sub': sub,
      'typ': typ,
      'azp': azp,
      'session_state': sessionState,
      'acr': acr,
      'allowed-origins': allowedOrigins,
      'realm_access': realmAccess.toJson(),
      'resource_access': resourceAccess.map(
        (key, value) => MapEntry(key, value.toJson()),
      ),
      'scope': scope,
      'sid': sid,
      'role': role,
      'email_verified': emailVerified,
      'name': name,
      'preferred_username': preferredUsername,
      'given_name': givenName,
      'userId': userId,
      'family_name': familyName,
      'email': email,
    };
  }
}

class RealmAccess {
  final List<String> roles;

  RealmAccess({required this.roles});

  factory RealmAccess.fromJson(Map<String, dynamic> json) {
    return RealmAccess(
      roles: List<String>.from(json['roles'] as List),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'roles': roles,
    };
  }
}

class ResourceAccess {
  final List<String> roles;

  ResourceAccess({required this.roles});

  factory ResourceAccess.fromJson(Map<String, dynamic> json) {
    return ResourceAccess(
      roles: List<String>.from(json['roles'] as List),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'roles': roles,
    };
  }
}