import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';

class PasswordVisibilityIcon extends StatelessWidget {
  final bool isVisible;
  final VoidCallback onTap;
  const PasswordVisibilityIcon({
    super.key,
    required this.isVisible,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: Icon(
        color: AppColors.primaryTheme,
        isVisible ? Icons.visibility : Icons.visibility_off),
      onPressed: onTap,
    );
  }
}
