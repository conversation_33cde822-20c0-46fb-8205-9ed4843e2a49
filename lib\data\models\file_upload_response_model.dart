import '../../domain/entities/file_upload_response.dart';

class FileUploadResponseModel extends FileUploadResponse {
  const FileUploadResponseModel({
    required super.fileId,
    required super.fileName,
    required super.uploadedFileName,
    required super.message,
  });

  factory FileUploadResponseModel.fromJson(Map<String, dynamic> json) {
    final messageList = json['message'] as List<dynamic>;
    final firstMessage = messageList.first as Map<String, dynamic>;
    
    return FileUploadResponseModel(
      fileId: firstMessage['fileId'] as String,
      fileName: firstMessage['filename'] as String,
      uploadedFileName: firstMessage['uploadedFileName'] as String,
      message: firstMessage['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fileId': fileId,
      'fileName': fileName,
      'uploadedFileName': uploadedFileName,
      'message': message,
    };
  }
}
