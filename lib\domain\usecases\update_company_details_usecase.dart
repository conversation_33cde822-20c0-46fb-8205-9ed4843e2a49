import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../data/models/update_company_details_response.dart';
import '../repositories/profile_marchant_repository.dart';

class UpdateCompanyDetailsUseCase {
  final ProfileMarchantRepository repository;

  UpdateCompanyDetailsUseCase(this.repository);

  Future<Either<Failure, UpdateCompanyDetailsResponse>> call(UpdateCompanyDetailsParams params) async {
    return await repository.updateCompanyDetails(
      companyName: params.companyName,
      logo: params.logo,
      description: params.description,
      detailedDescription: params.detailedDescription,
    );
  }
}

class UpdateCompanyDetailsParams {
  final String companyName;
  final String logo;
  final String description;
  final String detailedDescription;

  UpdateCompanyDetailsParams({
    required this.companyName,
    required this.logo,
    required this.description,
    required this.detailedDescription,
  });
}
