import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../data/models/profile_marchant_model.dart';
import '../repositories/profile_marchant_repository.dart';

class GetProfileUseCase {
  final ProfileMarchantRepository repository;

  GetProfileUseCase(this.repository);

  Future<Either<Failure, ProfileMarchantModel>> call() async {
    return await repository.getProfile();
  }
}
