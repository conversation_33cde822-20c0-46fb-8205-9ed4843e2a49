import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/img_string.dart';
import 'package:wesell/presentation/auth/pages/home_page.dart';
import 'package:wesell/presentation/settings/pages/settings_page.dart';
import 'package:wesell/presentation/tabs/bloc/tabs_bloc/tabs_bloc.dart';
import 'package:wesell/presentation/tabs/bloc/tabs_bloc/tabs_event.dart';
import 'package:wesell/presentation/tabs/bloc/tabs_bloc/tabs_state.dart';
import 'package:wesell/presentation/widgets/custom_svg_widget.dart';


class TabsPage extends StatelessWidget {
  const TabsPage({super.key});

  static final List<Widget> _screens = [
     HomePage(),
     Center(child: Text('seller'),),
     Center(child: Text('jobs'),),
     Center(child: Text('chat'),),
     SettingsPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TabsBloc, TabsState>(
      builder: (context, state) {
        return Scaffold(
          body: _screens[state.selectedIndex],
          bottomNavigationBar: BottomNavigationBar(
            currentIndex: state.selectedIndex,
            selectedItemColor: Colors.green,
            unselectedItemColor: Colors.grey,
            type: BottomNavigationBarType.fixed,
            showSelectedLabels: false, 
            showUnselectedLabels: false,
            onTap: (index) =>
                context.read<TabsBloc>().add(TabChanged(index)),
            items: [
              BottomNavigationBarItem(
                icon: SvgIcon(
                  assetPath: ImageStrings.home,
                  color: state.selectedIndex == 0 ? AppColors.primaryTheme : AppColors.iconLightTheme,
                ),
                label: '',
              ),
              BottomNavigationBarItem(
                icon: SvgIcon(
                  assetPath: ImageStrings.seller,
                  color: state.selectedIndex == 1 ? AppColors.primaryTheme : AppColors.iconLightTheme,
                ),
                label: '',
              ),
              BottomNavigationBarItem(
                icon: SvgIcon(
                  assetPath: ImageStrings.jobs,
                  color: state.selectedIndex == 2 ? AppColors.primaryTheme : AppColors.iconLightTheme,
                ),
                label: '',
              ),
              BottomNavigationBarItem(
                icon: SvgIcon(
                  assetPath: ImageStrings.chat,
                  color: state.selectedIndex == 3 ? AppColors.primaryTheme : AppColors.iconLightTheme,
                ),
                label: '',
              ),
              BottomNavigationBarItem(
                icon: SvgIcon(
                  assetPath: ImageStrings.settings,
                  color: state.selectedIndex == 4 ? AppColors.primaryTheme : AppColors.iconLightTheme,
                ),
                label: '',
              ),
            ],
          ),
        );
      },
    );
  }
}
