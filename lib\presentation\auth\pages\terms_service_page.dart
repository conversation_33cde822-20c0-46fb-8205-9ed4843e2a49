import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:wesell/core/constants/app_colors.dart';

class TermsOfServicePage extends StatefulWidget {
  const TermsOfServicePage({super.key});

  @override
  State<TermsOfServicePage> createState() => _TermsOfServicePageState();
}

class _TermsOfServicePageState extends State<TermsOfServicePage> {
  @override
  Widget build(BuildContext context) {
    const String dummyHtml = """
      <h2>Terms of Service</h2>
      <p>Welcome to <b>WeSell</b>! Please read these terms of service carefully.</p>
      <ul>
        <li>You must be at least 18 years old to use this app.</li>
        <li>Respect other users and do not post inappropriate content.</li>
        <li>Your data may be used in accordance with our privacy policy.</li>
      </ul>
      <p>For more information, contact <a href=\"mailto:<EMAIL>\"><EMAIL></a>.</p>
    """;

    return Scaffold(
      backgroundColor: AppColors.whiteTheme,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Html(data: dummyHtml),
      ),
    );
  }
}