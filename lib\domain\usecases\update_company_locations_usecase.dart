import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../data/models/update_company_details_response.dart';
import '../repositories/profile_marchant_repository.dart';

class UpdateCompanyLocationsUseCase {
  final ProfileMarchantRepository repository;

  UpdateCompanyLocationsUseCase(this.repository);

  Future<Either<Failure, UpdateCompanyDetailsResponse>> call(UpdateCompanyLocationsParams params) async {
    return await repository.updateCompanyLocations(
      country: params.country,
      city: params.city,
      address: params.address,
      poBox: params.poBox,
    );
  }
}

class UpdateCompanyLocationsParams {
  final String country;
  final String city;
  final String address;
  final String poBox;

  UpdateCompanyLocationsParams({
    required this.country,
    required this.city,
    required this.address,
    required this.poBox,
  });
}
