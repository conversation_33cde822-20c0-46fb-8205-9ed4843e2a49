import 'package:flutter/material.dart';

class CustomDottedBorder extends StatelessWidget {
  final Widget child;
  final double borderRadius;
  final double dashWidth;
  final double dashSpace;
  final Color borderColor;
  final double strokeWidth;

  const CustomDottedBorder({
    super.key,
    required this.child,
    this.borderRadius = 12.0,
    this.dashWidth = 5.0,
    this.dashSpace = 3.0,
    this.borderColor = Colors.black26,
    this.strokeWidth = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      foregroundPainter: _RoundedDottedBorderPainter(
        borderRadius: borderRadius,
        dashWidth: dashWidth,
        dashSpace: dashSpace,
        borderColor: borderColor,
        strokeWidth: strokeWidth,
      ),
      child: child,
    );
  }
}

class _RoundedDottedBorderPainter extends CustomPainter {
  final double borderRadius;
  final double dashWidth;
  final double dashSpace;
  final Color borderColor;
  final double strokeWidth;

  _RoundedDottedBorderPainter({
    required this.borderRadius,
    required this.dashWidth,
    required this.dashSpace,
    required this.borderColor,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = borderColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final rrect = RRect.fromRectAndRadius(
      Offset.zero & size,
      Radius.circular(borderRadius),
    );

    final path = Path()..addRRect(rrect);
    final metrics = path.computeMetrics();

    for (final metric in metrics) {
      double distance = 0.0;
      while (distance < metric.length) {
        final next = distance + dashWidth;
        canvas.drawPath(
          metric.extractPath(distance, next),
          paint,
        );
        distance = next + dashSpace;
      }
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
