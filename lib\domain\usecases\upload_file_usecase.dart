import 'dart:io';
import 'package:dartz/dartz.dart';
import '../entities/file_upload_response.dart';
import '../repositories/media_repository.dart';
import '../../core/error/failures.dart';

class UploadFileUseCase {
  final MediaRepository repository;

  UploadFileUseCase(this.repository);

  Future<Either<Failure, FileUploadResponse>> call(UploadFileParams params) async {
    return await repository.uploadFile(
      file: params.file,
      fileName: params.fileName,
    );
  }
}

class UploadFileParams {
  final File file;
  final String fileName;

  UploadFileParams({
    required this.file,
    required this.fileName,
  });
}
