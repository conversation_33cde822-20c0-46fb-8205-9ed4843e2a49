import '../../../data/models/profile_marchant_model.dart';

abstract class ProfileMarchantState {}

class ProfileMarchantInitial extends ProfileMarchantState {}

class ProfileMarchantLoading extends ProfileMarchantState {}

class ProfileMarchantLoaded extends ProfileMarchantState {
  final ProfileMarchantModel profile;

  ProfileMarchantLoaded({required this.profile});
}

class ProfileMarchantError extends ProfileMarchantState {
  final String message;

  ProfileMarchantError({required this.message});
}

class ImageUploadLoading extends ProfileMarchantState {}

class ImageUploadSuccess extends ProfileMarchantState {
  final String fileId;
  final String fileName;

  ImageUploadSuccess({
    required this.fileId,
    required this.fileName,
  });
}

class ImageUploadError extends ProfileMarchantState {
  final String message;

  ImageUploadError({required this.message});
}

class UpdateCompanyDetailsLoading extends ProfileMarchantState {}

class UpdateCompanyDetailsSuccess extends ProfileMarchantState {
  final String message;

  UpdateCompanyDetailsSuccess({required this.message});
}

class UpdateCompanyDetailsError extends ProfileMarchantState {
  final String message;

  UpdateCompanyDetailsError({required this.message});
}

class UpdateIndustryDetailsLoading extends ProfileMarchantState {}

class UpdateIndustryDetailsSuccess extends ProfileMarchantState {
  final String message;

  UpdateIndustryDetailsSuccess({required this.message});
}

class UpdateIndustryDetailsError extends ProfileMarchantState {
  final String message;

  UpdateIndustryDetailsError({required this.message});
}

class UpdateCompanyLocationsLoading extends ProfileMarchantState {}

class UpdateCompanyLocationsSuccess extends ProfileMarchantState {
  final String message;

  UpdateCompanyLocationsSuccess({required this.message});
}

class UpdateCompanyLocationsError extends ProfileMarchantState {
  final String message;

  UpdateCompanyLocationsError({required this.message});
}
