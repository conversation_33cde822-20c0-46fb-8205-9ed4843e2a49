import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/routes/app_routes.dart';
import 'package:wesell/presentation/auth/bloc/signup/signup_state.dart';
import 'package:wesell/presentation/auth/pages/sign_up_page.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/presentation/widgets/custom_text_field.dart';
import 'package:wesell/presentation/widgets/custom_dropdown_form_field.dart';
import 'package:wesell/presentation/widgets/file_upload_widget.dart';
import 'package:wesell/core/utils/validators.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/presentation/auth/bloc/signup/signup_bloc.dart';
import 'package:wesell/presentation/auth/bloc/signup/signup_event.dart';

class MerchantProfilePage extends StatefulWidget {
  final MerchantProfileArguments args;

  const MerchantProfilePage({super.key, required this.args});

  @override
  State<MerchantProfilePage> createState() => _MerchantProfilePageState();
}

class _MerchantProfilePageState extends State<MerchantProfilePage> {
  final _formKey = GlobalKey<FormState>();

  final _companyNameController = TextEditingController();
  final _crNumberController = TextEditingController();
  final _capitalController = TextEditingController();
  String? _selectedCompanySize;
  String? _selectedCurrencyUnit;
  String? licenseDocumentId;
  String? crDocumentId;
  final List<String> _companySizes = ['[0-50]', '[50-100]', '[100-200]', '[300+]'];
  final List<String> _currencyUnits = ['SAR'];

  @override
  void dispose() {
    _companyNameController.dispose();
    _crNumberController.dispose();
    _capitalController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SignupBloc, SignupState>(
      listener: (context, state) {
        if (state is SignupSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
          AppRoutes.pushRoot(context, AppRoutes.login);
        } else if (state is SignupFailure) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.error)),
          );
        }
      },
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: Scaffold(
          backgroundColor: AppColors.whiteTheme,
          appBar: AppBar(
            elevation: 0,
            backgroundColor: AppColors.whiteTheme,
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
            child: Form(
              key: _formKey,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppStrings.addCompanyInfo,
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryTheme,
                    ),
                  ),
                  const SizedBox(height: 24),
                  CustomTextField(
                    controller: _companyNameController,
                    hintText: AppStrings.companyLegalName,
                    validator: (value) => Validators.validateRequired(value, AppStrings.companyLegalName),
                  ),
                  const SizedBox(height: 16),
        
                  CustomDropdownFormField<String>(
                    value: _selectedCompanySize,
                    items: _companySizes,
                    hintText: AppStrings.selectCompanySize,
                    itemLabel: (item) => item,
                    validator: (value) => value == null ? AppStrings.pleaseSelectCompanySize : null,
                    onChanged: (val) => setState(() => _selectedCompanySize = val),
                  ),
                  const SizedBox(height: 16),
        
                  CustomTextField(
                    controller: _crNumberController,
                    hintText: AppStrings.crNumber,
                    validator: (value) => Validators.validateMinLength(value,10, AppStrings.crNumber),
                  ),
                  const SizedBox(height: 16),
        
                  CustomTextField(
                    controller: _capitalController,
                    hintText: AppStrings.companyInvestedCapital,
                    keyboardType: TextInputType.number,
                    validator: (value) =>
                        Validators.validateRequired(value, AppStrings.companyInvestedCapital),
                  ),
                  const SizedBox(height: 16),
        
                  CustomDropdownFormField<String>(
                    value: _selectedCurrencyUnit,
                    items: _currencyUnits,
                    hintText: AppStrings.selectUnit,
                    itemLabel: (item) => item,
                    onChanged: (val) => setState(() => _selectedCurrencyUnit = val),
                  ),
                  const SizedBox(height: 24),

                  FileUploadWidget(
                    label: AppStrings.attachCrDocuments,
                    uploadId: 'cr_document',
                    onFileUploaded: (fileId) {
                      setState(() {
                        crDocumentId = fileId;
                      });
                    },
                    currentFileId: crDocumentId,
                  ),

                  const SizedBox(height: 16),

                  FileUploadWidget(
                    label: AppStrings.attachCompanyLicenseDocuments,
                    uploadId: 'license_document',
                    onFileUploaded: (fileId) {
                      setState(() {
                        licenseDocumentId = fileId;
                      });
                    },
                    currentFileId: licenseDocumentId,
                  ),
                  const SizedBox(height: 32),
                  BlocBuilder<SignupBloc, SignupState>(
                      builder: (context, state) {
                        return CustomButton(
                          height: 48,
                          width: double.infinity,
                          backgroundColor: AppColors.primaryTheme,
                          text: AppStrings.createMyAccount,
                          onPressed: state is SignupLoading ? null : _handleSignup,
                              isLoading: state is SignupLoading,
                        );
                      }
                    )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }



  void _handleSignup() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<SignupBloc>().add(
        SignupSubmitted(
          firstName: _companyNameController.text.trim(),
          lastName: widget.args.lastName ?? '', // or another controller/field if needed
          email: widget.args.email ?? '', // or another controller/field if needed
          password: widget.args.password ?? '', // or another controller/field if needed
          type: widget.args.type ?? '', // or widget.selectedRole if available
          companyLegalName: _companyNameController.text.trim(),
          crNumber: _crNumberController.text.trim(),
          investedCapital: int.tryParse(_capitalController.text.trim()),
          investedCapitalUnit: _selectedCurrencyUnit,
          companySize: _selectedCompanySize,
          licenseDocument: licenseDocumentId,
          crDocument: crDocumentId,
        ),
      );
    }
  }
}
