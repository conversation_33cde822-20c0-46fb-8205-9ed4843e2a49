import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/constants/app_constants.dart';
import '../utils/file_utils.dart';

class ImageUploadService {
  static final ImagePicker _picker = ImagePicker();

  /// Pick image from gallery with validation
  /// Returns File if successful, null if cancelled or invalid
  static Future<File?> pickImageFromGallery() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        final file = File(pickedFile.path);
        final fileName = pickedFile.name;

        // Validate file using existing FileUtils
        final validationError = _validateImageFile(file, fileName);
        if (validationError != null) {
          throw Exception(validationError);
        }

        return file;
      }
      return null;
    } catch (e) {
      throw Exception('${AppConstants.imagePickFailed}: ${e.toString()}');
    }
  }

  /// Pick image from camera with validation
  /// Returns File if successful, null if cancelled or invalid
  static Future<File?> pickImageFromCamera() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        final file = File(pickedFile.path);
        final fileName = pickedFile.name;

        // Validate file using existing FileUtils
        final validationError = _validateImageFile(file, fileName);
        if (validationError != null) {
          throw Exception(validationError);
        }

        return file;
      }
      return null;
    } catch (e) {
      throw Exception('${AppConstants.imageCaptureFailed}: ${e.toString()}');
    }
  }

  /// Show image source selection dialog
  /// Returns File if successful, null if cancelled
  static Future<File?> showImageSourceDialog(context) async {
    return await showDialog<File?>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(AppStrings.selectImageSource),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text(AppStrings.gallery),
                onTap: () async {
                  Navigator.of(context).pop();
                  final file = await pickImageFromGallery();
                  Navigator.of(context).pop(file);
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text(AppStrings.camera),
                onTap: () async {
                  Navigator.of(context).pop();
                  final file = await pickImageFromCamera();
                  Navigator.of(context).pop(file);
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(AppStrings.cancel),
            ),
          ],
        );
      },
    );
  }

  /// Validate image file specifically for images
  static String? _validateImageFile(File file, String fileName) {
    // Check file format - only allow image formats
    final allowedImageExtensions = ['.jpg', '.jpeg', '.png'];
    final extension = fileName.toLowerCase().split('.').last;
    
    if (!allowedImageExtensions.contains('.$extension')) {
      return AppStrings.onlyJpgJpegPngAllowed;
    }
    
    // Check file size (max 10MB)
    if (!FileUtils.isValidFileSize(file)) {
      return AppStrings.imageSizeLimit;
    }
    
    return null; // File is valid
  }

  /// Generate a unique filename for the uploaded image
  static String generateImageFileName(String originalFileName) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = originalFileName.split('.').last;
    return 'image_${timestamp}.$extension';
  }
}
