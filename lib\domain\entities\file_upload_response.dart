import 'package:equatable/equatable.dart';

class FileUploadResponse extends Equatable {
  final String fileId;
  final String fileName;
  final String uploadedFileName;
  final String message;

  const FileUploadResponse({
    required this.fileId,
    required this.fileName,
    required this.uploadedFileName,
    required this.message,
  });

  @override
  List<Object> get props => [fileId, fileName, uploadedFileName, message];
}
