import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/routes/app_routes.dart';
import 'package:wesell/presentation/auth/bloc/signup/signup_bloc.dart';
import 'package:wesell/presentation/auth/bloc/signup/signup_event.dart';
import 'package:wesell/presentation/auth/bloc/signup/signup_state.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/presentation/widgets/custom_text_field.dart';
import 'package:wesell/presentation/widgets/password_visibility_icon.dart';
import '../../../core/utils/validators.dart';

class SignUpPage extends StatefulWidget {
  final String selectedRole;
  const SignUpPage({super.key, required this.selectedRole});

  @override
  State<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController(text: '');
  final _lastNameController = TextEditingController(text: '');
  final _emailController = TextEditingController(text: '');
  final _passwordController = TextEditingController(text: '');
  final _confirmPasswordController = TextEditingController(text: '');
  bool _agreeToTerms = false;
  bool _showPassword = false;
  bool _showConfirmPassword = false;

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SignupBloc, SignupState>(
      listener: (context, state) {
        if (state is SignupSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
          AppRoutes.pushRoot(context, AppRoutes.login);
        } else if (state is SignupFailure) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.error)),
          );
        }
      },
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Scaffold(
          backgroundColor: AppColors.whiteTheme,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 40.0),
            child: Form(
              autovalidateMode: AutovalidateMode.onUserInteraction,
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    AppStrings.signUp,
                    style: TextStyle(
                      color: AppColors.primaryTheme,
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 30),
                  CustomTextField(
                    controller: _firstNameController,
                    labelText: AppStrings.firstName,
                    hintText: AppStrings.firstName,
                    validator: (value) => Validators.validateRequired(value, AppStrings.firstName),
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    controller: _lastNameController,
                    labelText: AppStrings.lastName,
                    hintText: AppStrings.lastName,
                    validator: (value) => Validators.validateRequired(value, AppStrings.lastName),
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    controller: _emailController,
                    labelText: AppStrings.email,
                    hintText: AppStrings.email,
                    keyboardType: TextInputType.emailAddress,
                    validator: Validators.validateEmail,
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    controller: _passwordController,
                    labelText: AppStrings.password,
                    hintText: AppStrings.password,
                    obscureText: !_showPassword,
                    suffixIcon: PasswordVisibilityIcon(
                      isVisible: _showPassword,
                      onTap: () {
                        setState(() {
                          _showPassword = !_showPassword;
                        });
                      },
                    ),
                    validator: Validators.validatePassword,
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    controller: _confirmPasswordController,
                    labelText: AppStrings.confirmPassword,
                    hintText: AppStrings.confirmPassword,
                    obscureText: !_showConfirmPassword,
                    suffixIcon: PasswordVisibilityIcon(
                      isVisible: _showConfirmPassword,
                      onTap: () {
                        setState(() {
                          _showConfirmPassword = !_showConfirmPassword;
                        });
                      },
                    ),
                    validator: (value) => Validators.validateConfirmPassword(value, _passwordController.text),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Checkbox(
                        fillColor: MaterialStateProperty.all(AppColors.primaryTheme),
                        value: _agreeToTerms,
                        onChanged: (val) {
                          setState(() {
                            _agreeToTerms = val ?? false;
                          });
                        },
                      ),
                      Expanded(
                        child:
                        Text.rich(
                          TextSpan(
                            text: AppStrings.yes,
                            style: TextStyle(
                              fontSize: 16,
                              color: AppColors.textgreyTheme,
                            ),
                            children: [
                              TextSpan(
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                   // Navigate to  Wesell Terms of Service screen
                                    AppRoutes.pushScreen(context, AppRoutes.termsOfService);
                                  },
                                text: AppStrings.termsOfService,
                                style: const TextStyle(color:  AppColors.primaryTheme,fontSize: 16,),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  widget.selectedRole != 'merchant'?
                  BlocBuilder<SignupBloc, SignupState>(
                    builder: (context, state) {
                      return CustomButton(
                        height: 48,
                        width: double.infinity,
                        backgroundColor: AppColors.primaryTheme,
                        text: AppStrings.createMyAccount,
                        onPressed: state is SignupLoading ? null : _handleSignup,
                            isLoading: state is SignupLoading,
                      );
                    }
                  ):CustomButton(
                    height: 48,
                    width: double.infinity,
                    backgroundColor: AppColors.primaryTheme,
                    text: AppStrings.continueMerchant,
                    onPressed:  _handleSignup,
                  ),
                  const SizedBox(height: 20),
                  Center(
                    child: Text.rich(
                      TextSpan(
                        text: AppStrings.alreadyHaveAccount,
                        style: TextStyle(
                          fontSize: 16,
                          color: AppColors.textgreyTheme,
                        ),
                        children: [
                          TextSpan(
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                AppRoutes.pushRoot(context, AppRoutes.login);
                              },
                            text: AppStrings.signIn,
                            style: const TextStyle(color: AppColors.primaryTheme, fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleSignup() {
    if (_formKey.currentState?.validate() ?? false) {
      if (!_agreeToTerms) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppStrings.mustAgreeTerms)),
        );
        return;
      }
      if( widget.selectedRole == 'merchant'){
        //Navigate to Merchant Profile Page
        Navigator.pushNamed(context, AppRoutes.merchantProfile,
        //pass via model
          arguments: MerchantProfileArguments(
            firstName: _firstNameController.text.trim(),
            lastName: _lastNameController.text.trim(),
            email: _emailController.text.trim(),
            password: _passwordController.text,
            type: widget.selectedRole,
          ),
        );
      }else{
        context.read<SignupBloc>().add(
          SignupSubmitted(
            firstName: _firstNameController.text.trim(),
            lastName: _lastNameController.text.trim(),
            email: _emailController.text.trim(),
            password: _passwordController.text,
            type: widget.selectedRole,
          ),
        );
      }
      
    }
  }
}

class MerchantProfileArguments {
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? password;
  final String? type;

  MerchantProfileArguments({
    this.firstName,
    this.lastName,
    this.email,
    this.password,
    this.type,
  });
}
