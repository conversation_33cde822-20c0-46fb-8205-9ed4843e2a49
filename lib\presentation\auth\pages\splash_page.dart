import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/presentation/auth/bloc/login/login_event.dart';
import 'package:wesell/presentation/auth/bloc/login/login_state.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/routes/app_routes.dart';
import '../bloc/login/login_bloc.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  @override
  void initState() {
    super.initState();
    // Check login status when splash screen loads
    context.read<LoginBloc>().add(const CheckLoginStatus());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //backgroundColor: AppColors.primaryTheme,
      body: BlocListener<LoginBloc, LoginState>(
        listener: (context, state) {
          if (state is LoginSuccess) {
            // User is logged in, navigate to home
            AppRoutes.pushRoot(context, AppRoutes.tabs);
          } else if (state is LoginStatusChecked && !state.isLoggedIn) {
            // User is not logged in, navigate to login page
            AppRoutes.pushRoot(context, AppRoutes.login);
          }
        },
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App Logo
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: AppColors.whiteTheme,
                ),
              ),
              const SizedBox(height: 30),
        
              // Loading indicator
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.whiteTheme),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
