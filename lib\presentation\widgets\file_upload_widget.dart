import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/utils/file_utils.dart';
import '../auth/bloc/signup/signup_bloc.dart';
import '../auth/bloc/signup/signup_event.dart';
import '../auth/bloc/signup/signup_state.dart';

class FileUploadWidget extends StatefulWidget {
  final String label;
  final Function(String fileId) onFileUploaded;
  final String? currentFileId;
  final String? currentFileName;
  final String uploadId; // Unique identifier for this widget

  const FileUploadWidget({
    super.key,
    required this.label,
    required this.onFileUploaded,
    required this.uploadId,
    this.currentFileId,
    this.currentFileName,
  });

  @override
  State<FileUploadWidget> createState() => _FileUploadWidgetState();
}

class _FileUploadWidgetState extends State<FileUploadWidget> {
  String? _uploadedFileId;
  String? _uploadedFileName;
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _uploadedFileId = widget.currentFileId;
    _uploadedFileName = widget.currentFileName;
  }

  Future<void> _pickAndUploadFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final fileName = result.files.single.name;

        // Validate file
        final validationError = FileUtils.validateFile(file, fileName);
        if (validationError != null) {
          _showErrorSnackBar(validationError);
          return;
        }

        // Upload file
        if (mounted) {
          context.read<SignupBloc>().add(
            FileUploadSubmitted(
              file: file,
              fileName: fileName,
              uploadId: widget.uploadId,
            ),
          );
        }
      }
    } catch (e) {
      _showErrorSnackBar('Failed to pick file: ${e.toString()}');
    }
  }

  void _removeFile() {
    setState(() {
      _uploadedFileId = null;
      _uploadedFileName = null;
      _isUploading = false;
    });
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SignupBloc, SignupState>(
      listener: (context, state) {
        if (state is FileUploadLoading && state.uploadId == widget.uploadId) {
          setState(() {
            _isUploading = true;
          });
        } else if (state is FileUploadSuccess && state.uploadId == widget.uploadId) {
          setState(() {
            _uploadedFileId = state.fileId;
            _uploadedFileName = state.fileName;
            _isUploading = false;
          });
          widget.onFileUploaded(state.fileId);
          _showSuccessSnackBar(AppStrings.fileUploadSuccess);
        } else if (state is FileUploadFailure && state.uploadId == widget.uploadId) {
          setState(() {
            _isUploading = false;
          });
          _showErrorSnackBar(state.error);
        }
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 10),
        decoration: BoxDecoration(
          border: Border.all(
            color: _uploadedFileId != null ? Colors.green : Colors.grey,
            style: BorderStyle.solid,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: _uploadedFileId != null
            ? _buildUploadedFileWidget()
            : _buildUploadWidget(),
      ),
    );
  }

  Widget _buildUploadWidget() {
    return InkWell(
      onTap: _isUploading ? null : _pickAndUploadFile,
      child: Column(
        children: [
          Text(
            widget.label,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          if (_isUploading)
            const CircularProgressIndicator()
          else
            const Icon(
              Icons.upload_file,
              size: 40,
              color: AppColors.textgreyTheme,
            ),
          if (_isUploading)
            const Padding(
              padding: EdgeInsets.only(top: 8),
              child: Text('Uploading...'),
            ),
        ],
      ),
    );
  }

  Widget _buildUploadedFileWidget() {
    return Column(
      children: [
        Text(
          widget.label,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 20,
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                _uploadedFileName ?? 'File uploaded',
                style: const TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        // const SizedBox(height: 8),
        TextButton.icon(
          onPressed: _removeFile,
          icon: const Icon(Icons.delete, color: Colors.red, size: 16),
          label: const Text(
            AppStrings.removeFile,
            style: TextStyle(color: Colors.red),
          ),
        ),
      ],
    );
  }
}
