import 'dart:io';
import 'package:dio/dio.dart';
import '../../core/constants/app_constants.dart';
import '../../core/error/exceptions.dart';
import '../../core/network/dio_client.dart';
import '../models/file_upload_response_model.dart';

abstract class MediaRemoteDataSource {
  Future<FileUploadResponseModel> uploadFile({
    required File file,
    required String fileName,
  });
}

class MediaRemoteDataSourceImpl implements MediaRemoteDataSource {
  final DioClient dioClient;

  MediaRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<FileUploadResponseModel> uploadFile({
    required File file,
    required String fileName,
  }) async {
    try {
      final formData = FormData.fromMap({
        'files': await MultipartFile.fromFile(
          file.path,
          filename: fileName,
        ),
      });

      final response = await dioClient.post(
        AppConstants.mediaUploadEndpoint,
        data: formData,
      );

      return FileUploadResponseModel.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ServerException(
        message: e.response?.data['message'] ?? 'Upload failed',
        statusCode: e.response?.statusCode,
      );
    } catch (e) {
      throw ServerException(
        message: 'Upload failed: ${e.toString()}',
      );
    }
  }
}
